use candle_core::Tensor;
use candle_nn::VarBuilder;

#[derive(Clone)]
struct BertSelfOutput {
    dense: Linear,
    layer_norm: LayerNorm,
    dropout: Dropout,
    span: tracing::Span,
}

impl BertSelfOutput {
    fn dataset_var(in_dim: usize, out_dim: usize, vb: VarBuilder) -> anyhow::Result<Linear> {
        let init_ws = crate::init::DEFAULT_KAIMING_NORMAL;
        let ws = vb.get_with_hints((out_dim, in_dim), "weight", init_ws)?;
        let bound = 1. / (in_dim as f64).sqrt();
        let init_bs = crate::Init::Uniform {
            lo: -bound,
            up: bound,
        };
        let bs = vb.get_with_hints(out_dim, "bias", init_bs)?;
        Ok(Linear::new(ws, Some(bs)))
    }


    fn load(vb: VarBuilder, dataset: Tensor) -> anyhow::Result<Self> {
        let dense = candle_nn::linear(config.hidden_size, config.hidden_size, vb.pp("dense"))?;
        let layer_norm = layer_norm(
            config.hidden_size,
            config.layer_norm_eps,
            vb.pp("LayerNorm"),
        )?;
        let dropout = Dropout::new(config.hidden_dropout_prob);
        Ok(Self {
            dense,
            layer_norm,
            dropout,
            span: tracing::span!(tracing::Level::TRACE, "self-out"),
        })
    }

    fn forward(&self, hidden_states: &Tensor, input_tensor: &Tensor) -> Result<Tensor> {
        let _enter = self.span.enter();
        let hidden_states = self.dense.forward(hidden_states)?;
        let hidden_states = self.dropout.forward(&hidden_states)?;
        self.layer_norm.forward(&(hidden_states + input_tensor)?)
    }
}